from PIL import Image

def upscale_gif(input_path, output_path, target_size):
    """
    将 GIF 无损放大到指定尺寸（保持像素风格）
    :param input_path: 输入 GIF 路径
    :param output_path: 输出 GIF 路径
    :param target_size: 目标尺寸，如 (100, 100)
    """
    with Image.open(input_path) as im:
        frames = []
        durations = []
        disposals = []

        try:
            while True:
                # 获取当前帧和相关参数
                frame = im.convert("RGBA")
                duration = im.info.get('duration', 100)  # 默认 100ms
                disposal = im.disposal_method if hasattr(im, 'disposal_method') else 0

                # 放大帧（使用 NEAREST 保持像素清晰）
                resized = frame.resize(target_size, Image.NEAREST)
                frames.append(resized)
                durations.append(duration)
                disposals.append(disposal)

                im.seek(im.tell() + 1)
        except EOFError:
            pass  # 所有帧处理完毕

        # 保存新 GIF
        frames[0].save(
            output_path,
            save_all=True,
            append_images=frames[1:],
            duration=durations,
            loop=0,  # 0 表示无限循环
            disposal=disposals,
            optimize=False
        )

# 示例：将 20x20 的 GIF 放大到 100x100
input_gif = "nukoJoy.gif"
output_gif = "output_100x100.gif"
target_size = (100, 100)

upscale_gif(input_gif, output_gif, target_size)