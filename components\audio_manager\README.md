# Audio Manager

Audio Manager是一个高级音频管理组件，为ESP32系列芯片提供统一的音频录音和播放接口。它解耦了硬件依赖，提供了简洁易用的API。

## 功能特性

- **统一接口**: 提供简洁的录音和播放API
- **硬件抽象**: 屏蔽底层硬件差异，支持多种音频编解码器
- **状态管理**: 完善的音频状态跟踪和错误处理
- **资源管理**: 自动管理音频缓冲区和设备资源
- **多格式支持**: 支持多种采样率、位深和声道配置
- **回调机制**: 支持数据回调和事件回调
- **线程安全**: 内置互斥锁保护，支持多线程访问

## 架构设计

```
audio_manager
├── 核心管理层 (Core Manager)
│   ├── 设备初始化和配置
│   ├── 状态管理
│   └── 错误处理
├── 播放管理层 (Playback Manager)  
│   ├── PCM数据播放
│   ├── 音量控制
│   ├── 播放状态控制
│   └── 播放队列管理
├── 录音管理层 (Record Manager)
│   ├── 录音控制
│   ├── 数据缓冲
│   ├── 录音参数配置
│   └── 录音数据回调
└── 硬件抽象层接口
    ├── codec_board集成
    └── esp_codec_dev封装
```

## 快速开始

### 1. 基本初始化

```c
#include "audio_manager.h"

// 使用默认配置初始化
audio_manager_config_t config = AUDIO_MANAGER_DEFAULT_CONFIG();
audio_manager_handle_t manager = audio_manager_init(&config);

if (!manager) {
    ESP_LOGE(TAG, "Failed to initialize audio manager");
    return;
}
```

### 2. 音频播放

```c
// 配置播放参数
audio_playback_config_t playback_config = AUDIO_PLAYBACK_DEFAULT_CONFIG();
playback_config.volume = 70.0;
playback_config.format.sample_rate = 24000;
playback_config.format.channels = 2;
playback_config.format.bits_per_sample = 16;

// 开始播放
if (audio_manager_playback_start(manager, &playback_config) == AUDIO_MANAGER_OK) {
    // 写入音频数据
    int written = audio_manager_playback_write(manager, pcm_data, data_len, 1000);
    
    // 停止播放
    audio_manager_playback_stop(manager);
}
```

### 3. 音频录音

```c
// 录音数据回调函数
static size_t record_callback(const uint8_t *data, size_t len, void *user_ctx) {
    // 处理录音数据
    ESP_LOGI(TAG, "Received %d bytes of audio data", len);
    return len;
}

// 配置录音参数
audio_record_config_t record_config = AUDIO_RECORD_DEFAULT_CONFIG();
record_config.gain = 35.0;
record_config.data_cb = record_callback;

// 开始录音
if (audio_manager_record_start(manager, &record_config) == AUDIO_MANAGER_OK) {
    // 录音会通过回调函数处理数据
    vTaskDelay(pdMS_TO_TICKS(10000)); // 录音10秒
    
    // 停止录音
    audio_manager_record_stop(manager);
}
```

### 4. 音频回声

```c
// 同时启动播放和录音实现回声效果
audio_playback_config_t playback_config = AUDIO_PLAYBACK_DEFAULT_CONFIG();
audio_record_config_t record_config = AUDIO_RECORD_DEFAULT_CONFIG();

audio_manager_playback_start(manager, &playback_config);
audio_manager_record_start(manager, &record_config);

// 回声循环
uint8_t buffer[1024];
while (running) {
    int read_bytes = audio_manager_record_read(manager, buffer, sizeof(buffer), 100);
    if (read_bytes > 0) {
        audio_manager_playback_write(manager, buffer, read_bytes, 100);
    }
}
```

## API参考

### 初始化和清理

- `audio_manager_init()` - 初始化音频管理器
- `audio_manager_deinit()` - 清理音频管理器

### 播放控制

- `audio_manager_playback_start()` - 开始播放
- `audio_manager_playback_stop()` - 停止播放
- `audio_manager_playback_pause()` - 暂停播放
- `audio_manager_playback_resume()` - 恢复播放
- `audio_manager_playback_write()` - 写入播放数据
- `audio_manager_playback_set_volume()` - 设置音量
- `audio_manager_playback_get_state()` - 获取播放状态

### 录音控制

- `audio_manager_record_start()` - 开始录音
- `audio_manager_record_stop()` - 停止录音
- `audio_manager_record_pause()` - 暂停录音
- `audio_manager_record_resume()` - 恢复录音
- `audio_manager_record_read()` - 读取录音数据
- `audio_manager_record_set_gain()` - 设置录音增益
- `audio_manager_record_get_state()` - 获取录音状态

## 配置选项

### 音频格式配置

```c
typedef struct {
    uint32_t sample_rate;        // 采样率 (8000-48000)
    uint8_t  bits_per_sample;    // 位深 (16, 24, 32)
    uint8_t  channels;           // 声道数 (1=单声道, 2=立体声)
} audio_format_t;
```

### 管理器配置

```c
typedef struct {
    const char *board_type;              // 板型 (如 "C6_AMOLED_1_43")
    audio_format_t default_playback_fmt; // 默认播放格式
    audio_format_t default_record_fmt;   // 默认录音格式
    uint32_t playback_buffer_size;       // 播放缓冲区大小
    uint32_t record_buffer_size;         // 录音缓冲区大小
    uint8_t  task_priority;              // 任务优先级
    uint32_t task_stack_size;            // 任务栈大小
} audio_manager_config_t;
```

## 错误处理

所有API函数都返回错误码，可以根据返回值判断操作是否成功：

```c
audio_manager_err_t result = audio_manager_playback_start(manager, &config);
switch (result) {
    case AUDIO_MANAGER_OK:
        ESP_LOGI(TAG, "Operation successful");
        break;
    case AUDIO_MANAGER_ERR_INVALID_ARG:
        ESP_LOGE(TAG, "Invalid argument");
        break;
    case AUDIO_MANAGER_ERR_NOT_INITIALIZED:
        ESP_LOGE(TAG, "Audio manager not initialized");
        break;
    // ... 处理其他错误码
}
```

## 与现有代码的兼容性

Audio Manager设计为与现有的audio_bsp组件兼容。现有代码可以继续使用原有的API，同时新代码可以使用Audio Manager的高级功能。

## 依赖组件

- `codec_board` - 硬件抽象层
- `espressif__esp_codec_dev` - ESP音频编解码器驱动
- `freertos` - 实时操作系统
- `esp_common` - ESP通用组件
- `log` - 日志组件

## 示例代码

详细的使用示例请参考 `examples/audio_manager_example.c` 文件。
