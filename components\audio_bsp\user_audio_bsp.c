#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "user_audio_bsp.h"
#include "esp_log.h"
#include "esp_err.h"
#include "esp_heap_caps.h"

// Include new audio manager
#include "audio_manager.h"

// Legacy includes for backward compatibility
#include "codec_board.h"
#include "codec_init.h"

#define SAMPLE_RATE     24000           // 采样率：24000Hz
#define BIT_DEPTH       32              // 位深：32位

static const char *TAG = "USER_AUDIO_BSP";

// Legacy handles for backward compatibility
esp_codec_dev_handle_t playback = NULL;
esp_codec_dev_handle_t record = NULL;

// New audio manager handle
static audio_manager_handle_t g_audio_manager = NULL;


extern const uint8_t music_pcm_start[] asm("_binary_canon_pcm_start");
extern const uint8_t music_pcm_end[]   asm("_binary_canon_pcm_end");


void user_audio_bsp_init(void)
{
  // Initialize using new audio manager
  audio_manager_config_t config = AUDIO_MANAGER_DEFAULT_CONFIG();
  g_audio_manager = audio_manager_init(&config);

  if (g_audio_manager) {
    ESP_LOGI(TAG, "Audio manager initialized successfully");
  } else {
    ESP_LOGE(TAG, "Failed to initialize audio manager, falling back to legacy mode");

    // Fallback to legacy initialization for backward compatibility
    set_codec_board_type("C6_AMOLED_1_43");
    codec_init_cfg_t codec_dev = {0};
    init_codec(&codec_dev);
    playback = get_playback_handle();
    record = get_record_handle();
  }
}


void i2s_music(void *args)
{
  if (g_audio_manager) {
    // Use new audio manager
    audio_playback_config_t playback_config = AUDIO_PLAYBACK_DEFAULT_CONFIG();
    playback_config.format.sample_rate = 24000;
    playback_config.format.channels = 2;
    playback_config.format.bits_per_sample = 16;
    playback_config.volume = 60.0;
    playback_config.loop = true;

    // Start playback
    if (audio_manager_playback_start(g_audio_manager, &playback_config) == AUDIO_MANAGER_OK) {
      ESP_LOGI(TAG, "Started playback using audio manager");

      // Write music data
      size_t bytes_write = 0;
      size_t bytes_total = music_pcm_end - music_pcm_start;
      uint8_t *data_ptr = (uint8_t *)music_pcm_start;

      while (bytes_write < bytes_total) {
        size_t chunk_size = (bytes_total - bytes_write) > 256 ? 256 : (bytes_total - bytes_write);
        int written = audio_manager_playback_write(g_audio_manager, data_ptr, chunk_size, 1000);
        if (written > 0) {
          data_ptr += written;
          bytes_write += written;
        } else {
          ESP_LOGE(TAG, "Failed to write audio data");
          break;
        }
      }
    }
  } else {
    // Fallback to legacy implementation
    esp_codec_dev_set_out_vol(playback, 60.0);  //设置60声音大小
    for(;;)
    {
      size_t bytes_write = 0;
      size_t bytes_sizt = music_pcm_end - music_pcm_start;
      uint8_t *data_ptr = (uint8_t *)music_pcm_start;
      esp_codec_dev_sample_info_t fs = {
        .sample_rate = 24000,
        .channel = 2,
        .bits_per_sample = 16,
      };
      if(esp_codec_dev_open(playback, &fs) == ESP_CODEC_DEV_OK)
      {
        while (bytes_write < bytes_sizt)
        {
          esp_codec_dev_write(playback, data_ptr, 256);
          data_ptr += 256;
          bytes_write += 256;
        }
        //esp_codec_dev_close(playback); //close
      }
      else
      {
        break;
      }
    }
  }
  vTaskDelete(NULL);
}

// Audio data callback for echo functionality
static size_t echo_data_callback(const uint8_t *data, size_t len, void *user_ctx)
{
  audio_manager_handle_t *manager = (audio_manager_handle_t *)user_ctx;

  // Write the recorded data directly to playback
  if (manager && *manager) {
    int written = audio_manager_playback_write(*manager, data, len, 100);
    return (written > 0) ? written : 0;
  }
  return 0;
}

void i2s_echo(void *arg)
{
  if (g_audio_manager) {
    // Use new audio manager for echo
    audio_playback_config_t playback_config = AUDIO_PLAYBACK_DEFAULT_CONFIG();
    playback_config.format.sample_rate = SAMPLE_RATE;
    playback_config.format.channels = 2;
    playback_config.format.bits_per_sample = BIT_DEPTH;
    playback_config.volume = 90.0;

    audio_record_config_t record_config = AUDIO_RECORD_DEFAULT_CONFIG();
    record_config.format.sample_rate = SAMPLE_RATE;
    record_config.format.channels = 2;
    record_config.format.bits_per_sample = BIT_DEPTH;
    record_config.gain = 35.0;
    record_config.data_cb = echo_data_callback;
    record_config.user_ctx = &g_audio_manager;

    // Start playback and recording
    if (audio_manager_playback_start(g_audio_manager, &playback_config) == AUDIO_MANAGER_OK &&
        audio_manager_record_start(g_audio_manager, &record_config) == AUDIO_MANAGER_OK) {
      ESP_LOGI(TAG, "Echo mode started using audio manager");

      // Keep the task running
      while (1) {
        vTaskDelay(pdMS_TO_TICKS(1000));
      }
    } else {
      ESP_LOGE(TAG, "Failed to start echo mode");
    }
  } else {
    // Fallback to legacy implementation
    esp_codec_dev_set_out_vol(playback, 90.0); //设置90声音大小
    esp_codec_dev_set_in_gain(record, 35.0);   //设置录音时的增益
    uint8_t *data_ptr = (uint8_t *)heap_caps_malloc(1024 * sizeof(uint8_t), MALLOC_CAP_DEFAULT);
    esp_codec_dev_sample_info_t fs = {
      .sample_rate = SAMPLE_RATE,
      .channel = 2,
      .bits_per_sample = BIT_DEPTH,
    };
    esp_codec_dev_open(playback, &fs); //打开播放
    esp_codec_dev_open(record, &fs);   //打开录音
    for(;;)
    {
      if(ESP_CODEC_DEV_OK == esp_codec_dev_read(record, data_ptr, 1024))
      {
        esp_codec_dev_write(playback, data_ptr, 1024);
      }
    }
  }
}


void audio_playback_set_vol(uint8_t vol)
{
  if (g_audio_manager) {
    // Use new audio manager
    audio_manager_playback_set_volume(g_audio_manager, (float)vol);
  } else {
    // Fallback to legacy implementation
    esp_codec_dev_set_out_vol(playback, vol);   //设置声音大小
  }
}
















/*
Board: AMOLED_1_43
i2c: {sda: 18, scl: 8}
i2s: {bclk: 21, ws: 22, dout: 23, din: 20, mclk: 19}
out: {codec: ES8311, pa: -1, use_mclk: 1, pa_gain:6}
in: {codec: ES7210}
*/