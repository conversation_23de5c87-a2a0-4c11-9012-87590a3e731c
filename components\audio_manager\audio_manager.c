#include "audio_manager.h"
#include "codec_board.h"
#include "codec_init.h"
#include "esp_codec_dev.h"
#include "esp_log.h"
#include <string.h>
#include <stdlib.h>

static const char *TAG = "AUDIO_MANAGER";

/**
 * @brief Audio manager internal structure
 */
typedef struct audio_manager_s {
    // Configuration
    audio_manager_config_t config;
    
    // Hardware handles
    esp_codec_dev_handle_t playback_dev;
    esp_codec_dev_handle_t record_dev;
    
    // State management
    audio_playback_state_t playback_state;
    audio_record_state_t record_state;
    bool initialized;
    
    // Playback management
    audio_playback_config_t playback_config;
    TaskHandle_t playback_task;
    QueueHandle_t playback_queue;
    SemaphoreHandle_t playback_mutex;
    
    // Record management
    audio_record_config_t record_config;
    TaskHandle_t record_task;
    QueueHandle_t record_queue;
    SemaphoreHandle_t record_mutex;
    
    // Buffers
    uint8_t *playback_buffer;
    uint8_t *record_buffer;
} audio_manager_t;

/**
 * @brief Audio data message for queue
 */
typedef struct {
    uint8_t *data;
    size_t len;
    bool is_last;
} audio_data_msg_t;

// Static functions declarations
static audio_manager_err_t audio_manager_init_hardware(audio_manager_t *manager);
static void audio_manager_deinit_hardware(audio_manager_t *manager);
static void audio_playback_task(void *arg);
static void audio_record_task(void *arg);

audio_manager_handle_t audio_manager_init(const audio_manager_config_t *config)
{
    if (!config) {
        ESP_LOGE(TAG, "Invalid configuration");
        return NULL;
    }
    
    audio_manager_t *manager = calloc(1, sizeof(audio_manager_t));
    if (!manager) {
        ESP_LOGE(TAG, "Failed to allocate memory for audio manager");
        return NULL;
    }
    
    // Copy configuration
    memcpy(&manager->config, config, sizeof(audio_manager_config_t));
    
    // Initialize state
    manager->playback_state = AUDIO_PLAYBACK_STATE_IDLE;
    manager->record_state = AUDIO_RECORD_STATE_IDLE;
    
    // Create mutexes
    manager->playback_mutex = xSemaphoreCreateMutex();
    manager->record_mutex = xSemaphoreCreateMutex();
    if (!manager->playback_mutex || !manager->record_mutex) {
        ESP_LOGE(TAG, "Failed to create mutexes");
        goto error;
    }
    
    // Create queues
    manager->playback_queue = xQueueCreate(8, sizeof(audio_data_msg_t));
    manager->record_queue = xQueueCreate(8, sizeof(audio_data_msg_t));
    if (!manager->playback_queue || !manager->record_queue) {
        ESP_LOGE(TAG, "Failed to create queues");
        goto error;
    }
    
    // Allocate buffers
    manager->playback_buffer = malloc(config->playback_buffer_size);
    manager->record_buffer = malloc(config->record_buffer_size);
    if (!manager->playback_buffer || !manager->record_buffer) {
        ESP_LOGE(TAG, "Failed to allocate audio buffers");
        goto error;
    }
    
    // Initialize hardware
    if (audio_manager_init_hardware(manager) != AUDIO_MANAGER_OK) {
        ESP_LOGE(TAG, "Failed to initialize audio hardware");
        goto error;
    }
    
    manager->initialized = true;
    ESP_LOGI(TAG, "Audio manager initialized successfully");
    return manager;
    
error:
    audio_manager_deinit(manager);
    return NULL;
}

audio_manager_err_t audio_manager_deinit(audio_manager_handle_t handle)
{
    if (!handle) {
        return AUDIO_MANAGER_ERR_INVALID_ARG;
    }
    
    audio_manager_t *manager = (audio_manager_t *)handle;
    
    // Stop any ongoing operations
    audio_manager_playback_stop(handle);
    audio_manager_record_stop(handle);
    
    // Deinitialize hardware
    audio_manager_deinit_hardware(manager);
    
    // Clean up resources
    if (manager->playback_mutex) {
        vSemaphoreDelete(manager->playback_mutex);
    }
    if (manager->record_mutex) {
        vSemaphoreDelete(manager->record_mutex);
    }
    if (manager->playback_queue) {
        vQueueDelete(manager->playback_queue);
    }
    if (manager->record_queue) {
        vQueueDelete(manager->record_queue);
    }
    if (manager->playback_buffer) {
        free(manager->playback_buffer);
    }
    if (manager->record_buffer) {
        free(manager->record_buffer);
    }
    
    free(manager);
    ESP_LOGI(TAG, "Audio manager deinitialized");
    return AUDIO_MANAGER_OK;
}

static audio_manager_err_t audio_manager_init_hardware(audio_manager_t *manager)
{
    // Set board type
    set_codec_board_type(manager->config.board_type);
    
    // Initialize codec
    codec_init_cfg_t codec_cfg = {
        .in_mode = CODEC_I2S_MODE_STD,
        .out_mode = CODEC_I2S_MODE_STD,
        .in_use_tdm = false,
        .reuse_dev = false
    };
    
    if (init_codec(&codec_cfg) != 0) {
        ESP_LOGE(TAG, "Failed to initialize codec");
        return AUDIO_MANAGER_ERR_HARDWARE_FAIL;
    }
    
    // Get device handles
    manager->playback_dev = get_playback_handle();
    manager->record_dev = get_record_handle();
    
    if (!manager->playback_dev && !manager->record_dev) {
        ESP_LOGE(TAG, "Failed to get audio device handles");
        return AUDIO_MANAGER_ERR_DEVICE_NOT_FOUND;
    }
    
    // Set default volume and gain
    if (manager->playback_dev) {
        esp_codec_dev_set_out_vol(manager->playback_dev, 60.0);
    }
    if (manager->record_dev) {
        esp_codec_dev_set_in_gain(manager->record_dev, 35.0);
    }
    
    ESP_LOGI(TAG, "Audio hardware initialized successfully");
    return AUDIO_MANAGER_OK;
}

static void audio_manager_deinit_hardware(audio_manager_t *manager)
{
    if (manager->playback_dev) {
        esp_codec_dev_close(manager->playback_dev);
        manager->playback_dev = NULL;
    }
    if (manager->record_dev) {
        esp_codec_dev_close(manager->record_dev);
        manager->record_dev = NULL;
    }
    
    deinit_codec();
}

audio_manager_err_t audio_manager_playback_set_volume(audio_manager_handle_t handle, float volume)
{
    if (!handle) {
        return AUDIO_MANAGER_ERR_INVALID_ARG;
    }
    
    audio_manager_t *manager = (audio_manager_t *)handle;
    
    if (!manager->initialized || !manager->playback_dev) {
        return AUDIO_MANAGER_ERR_NOT_INITIALIZED;
    }
    
    if (volume < 0.0 || volume > 100.0) {
        return AUDIO_MANAGER_ERR_INVALID_ARG;
    }
    
    if (xSemaphoreTake(manager->playback_mutex, pdMS_TO_TICKS(1000)) != pdTRUE) {
        return AUDIO_MANAGER_ERR_TIMEOUT;
    }
    
    esp_err_t ret = esp_codec_dev_set_out_vol(manager->playback_dev, volume);
    if (ret == ESP_OK) {
        manager->playback_config.volume = volume;
    }
    
    xSemaphoreGive(manager->playback_mutex);
    
    return (ret == ESP_OK) ? AUDIO_MANAGER_OK : AUDIO_MANAGER_ERR_HARDWARE_FAIL;
}

audio_playback_state_t audio_manager_playback_get_state(audio_manager_handle_t handle)
{
    if (!handle) {
        return AUDIO_PLAYBACK_STATE_ERROR;
    }
    
    audio_manager_t *manager = (audio_manager_t *)handle;
    return manager->playback_state;
}

audio_manager_err_t audio_manager_record_set_gain(audio_manager_handle_t handle, float gain)
{
    if (!handle) {
        return AUDIO_MANAGER_ERR_INVALID_ARG;
    }
    
    audio_manager_t *manager = (audio_manager_t *)handle;
    
    if (!manager->initialized || !manager->record_dev) {
        return AUDIO_MANAGER_ERR_NOT_INITIALIZED;
    }
    
    if (gain < 0.0 || gain > 100.0) {
        return AUDIO_MANAGER_ERR_INVALID_ARG;
    }
    
    if (xSemaphoreTake(manager->record_mutex, pdMS_TO_TICKS(1000)) != pdTRUE) {
        return AUDIO_MANAGER_ERR_TIMEOUT;
    }
    
    esp_err_t ret = esp_codec_dev_set_in_gain(manager->record_dev, gain);
    if (ret == ESP_OK) {
        manager->record_config.gain = gain;
    }
    
    xSemaphoreGive(manager->record_mutex);
    
    return (ret == ESP_OK) ? AUDIO_MANAGER_OK : AUDIO_MANAGER_ERR_HARDWARE_FAIL;
}

audio_record_state_t audio_manager_record_get_state(audio_manager_handle_t handle)
{
    if (!handle) {
        return AUDIO_RECORD_STATE_ERROR;
    }

    audio_manager_t *manager = (audio_manager_t *)handle;
    return manager->record_state;
}

audio_manager_err_t audio_manager_playback_start(audio_manager_handle_t handle,
                                                  const audio_playback_config_t *config)
{
    if (!handle || !config) {
        return AUDIO_MANAGER_ERR_INVALID_ARG;
    }

    audio_manager_t *manager = (audio_manager_t *)handle;

    if (!manager->initialized || !manager->playback_dev) {
        return AUDIO_MANAGER_ERR_NOT_INITIALIZED;
    }

    if (xSemaphoreTake(manager->playback_mutex, pdMS_TO_TICKS(1000)) != pdTRUE) {
        return AUDIO_MANAGER_ERR_TIMEOUT;
    }

    // Stop any existing playback
    if (manager->playback_state != AUDIO_PLAYBACK_STATE_IDLE) {
        audio_manager_playback_stop(handle);
    }

    // Copy configuration
    memcpy(&manager->playback_config, config, sizeof(audio_playback_config_t));

    // Configure codec device
    esp_codec_dev_sample_info_t sample_info = {
        .sample_rate = config->format.sample_rate,
        .channel = config->format.channels,
        .bits_per_sample = config->format.bits_per_sample,
    };

    esp_err_t ret = esp_codec_dev_open(manager->playback_dev, &sample_info);
    if (ret != ESP_CODEC_DEV_OK) {
        ESP_LOGE(TAG, "Failed to open playback device");
        xSemaphoreGive(manager->playback_mutex);
        return AUDIO_MANAGER_ERR_HARDWARE_FAIL;
    }

    // Set volume
    esp_codec_dev_set_out_vol(manager->playback_dev, config->volume);

    // Create playback task
    BaseType_t task_ret = xTaskCreate(
        audio_playback_task,
        "audio_playback",
        manager->config.task_stack_size,
        manager,
        manager->config.task_priority,
        &manager->playback_task
    );

    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create playback task");
        esp_codec_dev_close(manager->playback_dev);
        xSemaphoreGive(manager->playback_mutex);
        return AUDIO_MANAGER_ERR_NO_MEM;
    }

    manager->playback_state = AUDIO_PLAYBACK_STATE_PLAYING;
    xSemaphoreGive(manager->playback_mutex);

    ESP_LOGI(TAG, "Playback started");
    return AUDIO_MANAGER_OK;
}

audio_manager_err_t audio_manager_playback_stop(audio_manager_handle_t handle)
{
    if (!handle) {
        return AUDIO_MANAGER_ERR_INVALID_ARG;
    }

    audio_manager_t *manager = (audio_manager_t *)handle;

    if (xSemaphoreTake(manager->playback_mutex, pdMS_TO_TICKS(1000)) != pdTRUE) {
        return AUDIO_MANAGER_ERR_TIMEOUT;
    }

    if (manager->playback_state == AUDIO_PLAYBACK_STATE_IDLE) {
        xSemaphoreGive(manager->playback_mutex);
        return AUDIO_MANAGER_OK;
    }

    manager->playback_state = AUDIO_PLAYBACK_STATE_STOPPED;

    // Delete playback task
    if (manager->playback_task) {
        vTaskDelete(manager->playback_task);
        manager->playback_task = NULL;
    }

    // Clear queue
    audio_data_msg_t msg;
    while (xQueueReceive(manager->playback_queue, &msg, 0) == pdTRUE) {
        if (msg.data) {
            free(msg.data);
        }
    }

    // Close device
    if (manager->playback_dev) {
        esp_codec_dev_close(manager->playback_dev);
    }

    manager->playback_state = AUDIO_PLAYBACK_STATE_IDLE;
    xSemaphoreGive(manager->playback_mutex);

    ESP_LOGI(TAG, "Playback stopped");
    return AUDIO_MANAGER_OK;
}

int audio_manager_playback_write(audio_manager_handle_t handle,
                                 const uint8_t *data,
                                 size_t len,
                                 uint32_t timeout_ms)
{
    if (!handle || !data || len == 0) {
        return -AUDIO_MANAGER_ERR_INVALID_ARG;
    }

    audio_manager_t *manager = (audio_manager_t *)handle;

    if (manager->playback_state != AUDIO_PLAYBACK_STATE_PLAYING) {
        return -AUDIO_MANAGER_ERR_INVALID_STATE;
    }

    // Allocate memory for data copy
    uint8_t *data_copy = malloc(len);
    if (!data_copy) {
        return -AUDIO_MANAGER_ERR_NO_MEM;
    }

    memcpy(data_copy, data, len);

    audio_data_msg_t msg = {
        .data = data_copy,
        .len = len,
        .is_last = false
    };

    if (xQueueSend(manager->playback_queue, &msg, pdMS_TO_TICKS(timeout_ms)) != pdTRUE) {
        free(data_copy);
        return -AUDIO_MANAGER_ERR_TIMEOUT;
    }

    return len;
}

static void audio_playback_task(void *arg)
{
    audio_manager_t *manager = (audio_manager_t *)arg;
    audio_data_msg_t msg;

    ESP_LOGI(TAG, "Playback task started");

    while (manager->playback_state == AUDIO_PLAYBACK_STATE_PLAYING) {
        if (xQueueReceive(manager->playback_queue, &msg, pdMS_TO_TICKS(100)) == pdTRUE) {
            if (msg.data && msg.len > 0) {
                // Write data to codec device
                esp_err_t ret = esp_codec_dev_write(manager->playback_dev, msg.data, msg.len);
                if (ret != ESP_CODEC_DEV_OK) {
                    ESP_LOGE(TAG, "Failed to write audio data");
                    manager->playback_state = AUDIO_PLAYBACK_STATE_ERROR;
                }

                // Free the data
                free(msg.data);

                // Call event callback if provided
                if (manager->playback_config.event_cb) {
                    manager->playback_config.event_cb(0, NULL, manager->playback_config.user_ctx);
                }
            }
        }
    }

    ESP_LOGI(TAG, "Playback task ended");
    vTaskDelete(NULL);
}

audio_manager_err_t audio_manager_record_start(audio_manager_handle_t handle,
                                                const audio_record_config_t *config)
{
    if (!handle || !config) {
        return AUDIO_MANAGER_ERR_INVALID_ARG;
    }

    audio_manager_t *manager = (audio_manager_t *)handle;

    if (!manager->initialized || !manager->record_dev) {
        return AUDIO_MANAGER_ERR_NOT_INITIALIZED;
    }

    if (xSemaphoreTake(manager->record_mutex, pdMS_TO_TICKS(1000)) != pdTRUE) {
        return AUDIO_MANAGER_ERR_TIMEOUT;
    }

    // Stop any existing recording
    if (manager->record_state != AUDIO_RECORD_STATE_IDLE) {
        audio_manager_record_stop(handle);
    }

    // Copy configuration
    memcpy(&manager->record_config, config, sizeof(audio_record_config_t));

    // Configure codec device
    esp_codec_dev_sample_info_t sample_info = {
        .sample_rate = config->format.sample_rate,
        .channel = config->format.channels,
        .bits_per_sample = config->format.bits_per_sample,
    };

    esp_err_t ret = esp_codec_dev_open(manager->record_dev, &sample_info);
    if (ret != ESP_CODEC_DEV_OK) {
        ESP_LOGE(TAG, "Failed to open record device");
        xSemaphoreGive(manager->record_mutex);
        return AUDIO_MANAGER_ERR_HARDWARE_FAIL;
    }

    // Set gain
    esp_codec_dev_set_in_gain(manager->record_dev, config->gain);

    // Create record task
    BaseType_t task_ret = xTaskCreate(
        audio_record_task,
        "audio_record",
        manager->config.task_stack_size,
        manager,
        manager->config.task_priority,
        &manager->record_task
    );

    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create record task");
        esp_codec_dev_close(manager->record_dev);
        xSemaphoreGive(manager->record_mutex);
        return AUDIO_MANAGER_ERR_NO_MEM;
    }

    manager->record_state = AUDIO_RECORD_STATE_RECORDING;
    xSemaphoreGive(manager->record_mutex);

    ESP_LOGI(TAG, "Recording started");
    return AUDIO_MANAGER_OK;
}

audio_manager_err_t audio_manager_record_stop(audio_manager_handle_t handle)
{
    if (!handle) {
        return AUDIO_MANAGER_ERR_INVALID_ARG;
    }

    audio_manager_t *manager = (audio_manager_t *)handle;

    if (xSemaphoreTake(manager->record_mutex, pdMS_TO_TICKS(1000)) != pdTRUE) {
        return AUDIO_MANAGER_ERR_TIMEOUT;
    }

    if (manager->record_state == AUDIO_RECORD_STATE_IDLE) {
        xSemaphoreGive(manager->record_mutex);
        return AUDIO_MANAGER_OK;
    }

    manager->record_state = AUDIO_RECORD_STATE_STOPPED;

    // Delete record task
    if (manager->record_task) {
        vTaskDelete(manager->record_task);
        manager->record_task = NULL;
    }

    // Clear queue
    audio_data_msg_t msg;
    while (xQueueReceive(manager->record_queue, &msg, 0) == pdTRUE) {
        if (msg.data) {
            free(msg.data);
        }
    }

    // Close device
    if (manager->record_dev) {
        esp_codec_dev_close(manager->record_dev);
    }

    manager->record_state = AUDIO_RECORD_STATE_IDLE;
    xSemaphoreGive(manager->record_mutex);

    ESP_LOGI(TAG, "Recording stopped");
    return AUDIO_MANAGER_OK;
}

static void audio_record_task(void *arg)
{
    audio_manager_t *manager = (audio_manager_t *)arg;

    ESP_LOGI(TAG, "Record task started");

    while (manager->record_state == AUDIO_RECORD_STATE_RECORDING) {
        // Read data from codec device
        esp_err_t ret = esp_codec_dev_read(manager->record_dev,
                                           manager->record_buffer,
                                           manager->config.record_buffer_size);

        if (ret == ESP_CODEC_DEV_OK) {
            // Call data callback if provided
            if (manager->record_config.data_cb) {
                size_t processed = manager->record_config.data_cb(
                    manager->record_buffer,
                    manager->config.record_buffer_size,
                    manager->record_config.user_ctx
                );

                if (processed == 0) {
                    // Callback indicated to stop recording
                    manager->record_state = AUDIO_RECORD_STATE_STOPPED;
                    break;
                }
            }

            // Call event callback if provided
            if (manager->record_config.event_cb) {
                manager->record_config.event_cb(0, NULL, manager->record_config.user_ctx);
            }
        } else {
            ESP_LOGE(TAG, "Failed to read audio data");
            manager->record_state = AUDIO_RECORD_STATE_ERROR;
            break;
        }

        vTaskDelay(pdMS_TO_TICKS(10)); // Small delay to prevent tight loop
    }

    ESP_LOGI(TAG, "Record task ended");
    vTaskDelete(NULL);
}

int audio_manager_record_read(audio_manager_handle_t handle,
                              uint8_t *data,
                              size_t len,
                              uint32_t timeout_ms)
{
    if (!handle || !data || len == 0) {
        return -AUDIO_MANAGER_ERR_INVALID_ARG;
    }

    audio_manager_t *manager = (audio_manager_t *)handle;

    if (manager->record_state != AUDIO_RECORD_STATE_RECORDING) {
        return -AUDIO_MANAGER_ERR_INVALID_STATE;
    }

    if (!manager->record_dev) {
        return -AUDIO_MANAGER_ERR_NOT_INITIALIZED;
    }

    esp_err_t ret = esp_codec_dev_read(manager->record_dev, data, len);
    if (ret != ESP_CODEC_DEV_OK) {
        return -AUDIO_MANAGER_ERR_HARDWARE_FAIL;
    }

    return len;
}

audio_manager_err_t audio_manager_playback_pause(audio_manager_handle_t handle)
{
    if (!handle) {
        return AUDIO_MANAGER_ERR_INVALID_ARG;
    }

    audio_manager_t *manager = (audio_manager_t *)handle;

    if (manager->playback_state != AUDIO_PLAYBACK_STATE_PLAYING) {
        return AUDIO_MANAGER_ERR_INVALID_STATE;
    }

    manager->playback_state = AUDIO_PLAYBACK_STATE_PAUSED;
    return AUDIO_MANAGER_OK;
}

audio_manager_err_t audio_manager_playback_resume(audio_manager_handle_t handle)
{
    if (!handle) {
        return AUDIO_MANAGER_ERR_INVALID_ARG;
    }

    audio_manager_t *manager = (audio_manager_t *)handle;

    if (manager->playback_state != AUDIO_PLAYBACK_STATE_PAUSED) {
        return AUDIO_MANAGER_ERR_INVALID_STATE;
    }

    manager->playback_state = AUDIO_PLAYBACK_STATE_PLAYING;
    return AUDIO_MANAGER_OK;
}

audio_manager_err_t audio_manager_record_pause(audio_manager_handle_t handle)
{
    if (!handle) {
        return AUDIO_MANAGER_ERR_INVALID_ARG;
    }

    audio_manager_t *manager = (audio_manager_t *)handle;

    if (manager->record_state != AUDIO_RECORD_STATE_RECORDING) {
        return AUDIO_MANAGER_ERR_INVALID_STATE;
    }

    manager->record_state = AUDIO_RECORD_STATE_PAUSED;
    return AUDIO_MANAGER_OK;
}

audio_manager_err_t audio_manager_record_resume(audio_manager_handle_t handle)
{
    if (!handle) {
        return AUDIO_MANAGER_ERR_INVALID_ARG;
    }

    audio_manager_t *manager = (audio_manager_t *)handle;

    if (manager->record_state != AUDIO_RECORD_STATE_PAUSED) {
        return AUDIO_MANAGER_ERR_INVALID_STATE;
    }

    manager->record_state = AUDIO_RECORD_STATE_RECORDING;
    return AUDIO_MANAGER_OK;
}
