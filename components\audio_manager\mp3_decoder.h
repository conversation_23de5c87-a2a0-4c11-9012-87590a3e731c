#ifndef MP3_DECODER_H
#define MP3_DECODER_H

#include <stdint.h>
#include <stddef.h>
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief MP3 decoder error codes
 */
typedef enum {
    MP3_DECODER_OK = 0,                    /*!< Success */
    MP3_DECODER_ERR_INVALID_ARG,           /*!< Invalid argument */
    MP3_DECODER_ERR_NO_MEM,                /*!< Out of memory */
    MP3_DECODER_ERR_DECODE_FAIL,           /*!< Decode failed */
    MP3_DECODER_ERR_EOF,                   /*!< End of file */
    MP3_DECODER_ERR_UNSUPPORTED_FORMAT,    /*!< Unsupported format */
} mp3_decoder_err_t;

/**
 * @brief MP3 frame info
 */
typedef struct {
    int sample_rate;        /*!< Sample rate */
    int channels;           /*!< Number of channels */
    int bitrate;            /*!< Bitrate in kbps */
    int layer;              /*!< MPEG layer */
    int frame_bytes;        /*!< Frame size in bytes */
} mp3_frame_info_t;

/**
 * @brief MP3 decoder handle
 */
typedef struct mp3_decoder_s *mp3_decoder_handle_t;

/**
 * @brief MP3 data callback function type
 * @param pcm_data Pointer to decoded PCM data
 * @param samples Number of samples (per channel)
 * @param info Frame information
 * @param user_ctx User context pointer
 * @return 0 to continue, non-zero to stop
 */
typedef int (*mp3_data_callback_t)(const int16_t *pcm_data, int samples, 
                                   const mp3_frame_info_t *info, void *user_ctx);

/**
 * @brief Initialize MP3 decoder
 * 
 * @return MP3 decoder handle on success, NULL on failure
 */
mp3_decoder_handle_t mp3_decoder_init(void);

/**
 * @brief Deinitialize MP3 decoder
 * 
 * @param handle MP3 decoder handle
 * @return MP3_DECODER_OK on success, error code on failure
 */
mp3_decoder_err_t mp3_decoder_deinit(mp3_decoder_handle_t handle);

/**
 * @brief Decode MP3 data
 * 
 * @param handle MP3 decoder handle
 * @param mp3_data MP3 data buffer
 * @param mp3_size MP3 data size
 * @param callback Data callback function
 * @param user_ctx User context for callback
 * @return MP3_DECODER_OK on success, error code on failure
 */
mp3_decoder_err_t mp3_decoder_decode(mp3_decoder_handle_t handle,
                                      const uint8_t *mp3_data,
                                      size_t mp3_size,
                                      mp3_data_callback_t callback,
                                      void *user_ctx);

/**
 * @brief Get MP3 file info
 * 
 * @param mp3_data MP3 data buffer
 * @param mp3_size MP3 data size
 * @param info Pointer to store frame info
 * @return MP3_DECODER_OK on success, error code on failure
 */
mp3_decoder_err_t mp3_decoder_get_info(const uint8_t *mp3_data,
                                        size_t mp3_size,
                                        mp3_frame_info_t *info);

#ifdef __cplusplus
}
#endif

#endif // MP3_DECODER_H
