/**
 * @file audio_manager_example.c
 * @brief Audio Manager usage examples
 */

#include "audio_manager.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char *TAG = "AUDIO_EXAMPLE";

// Example PCM data (sine wave)
static const uint8_t example_pcm_data[] = {
    // This would contain actual PCM audio data
    // For demonstration purposes, using placeholder data
    0x00, 0x00, 0x00, 0x00, 0x7F, 0x7F, 0x7F, 0x7F,
    0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0x7F, 0x7F, 0x7F,
    // ... more PCM data would go here
};

/**
 * @brief Audio data callback for recording
 */
static size_t record_data_callback(const uint8_t *data, size_t len, void *user_ctx)
{
    ESP_LOGI(TAG, "Received %d bytes of audio data", len);
    
    // Process the recorded audio data here
    // For example: save to file, send over network, etc.
    
    return len; // Return number of bytes processed
}

/**
 * @brief Audio event callback
 */
static void audio_event_callback(int event, void *data, void *user_ctx)
{
    ESP_LOGI(TAG, "Audio event: %d", event);
}

/**
 * @brief Example: Simple audio playback
 */
void audio_manager_playback_example(void)
{
    ESP_LOGI(TAG, "Starting playback example");
    
    // Initialize audio manager
    audio_manager_config_t config = AUDIO_MANAGER_DEFAULT_CONFIG();
    audio_manager_handle_t manager = audio_manager_init(&config);
    
    if (!manager) {
        ESP_LOGE(TAG, "Failed to initialize audio manager");
        return;
    }
    
    // Configure playback
    audio_playback_config_t playback_config = AUDIO_PLAYBACK_DEFAULT_CONFIG();
    playback_config.volume = 70.0;
    playback_config.event_cb = audio_event_callback;
    
    // Start playback
    if (audio_manager_playback_start(manager, &playback_config) == AUDIO_MANAGER_OK) {
        ESP_LOGI(TAG, "Playback started successfully");
        
        // Write audio data
        int written = audio_manager_playback_write(manager, 
                                                   example_pcm_data, 
                                                   sizeof(example_pcm_data), 
                                                   1000);
        if (written > 0) {
            ESP_LOGI(TAG, "Written %d bytes to playback", written);
        }
        
        // Let it play for a while
        vTaskDelay(pdMS_TO_TICKS(5000));
        
        // Stop playback
        audio_manager_playback_stop(manager);
        ESP_LOGI(TAG, "Playback stopped");
    }
    
    // Cleanup
    audio_manager_deinit(manager);
}

/**
 * @brief Example: Audio recording
 */
void audio_manager_record_example(void)
{
    ESP_LOGI(TAG, "Starting record example");
    
    // Initialize audio manager
    audio_manager_config_t config = AUDIO_MANAGER_DEFAULT_CONFIG();
    audio_manager_handle_t manager = audio_manager_init(&config);
    
    if (!manager) {
        ESP_LOGE(TAG, "Failed to initialize audio manager");
        return;
    }
    
    // Configure recording
    audio_record_config_t record_config = AUDIO_RECORD_DEFAULT_CONFIG();
    record_config.gain = 40.0;
    record_config.duration_ms = 10000; // Record for 10 seconds
    record_config.data_cb = record_data_callback;
    record_config.event_cb = audio_event_callback;
    
    // Start recording
    if (audio_manager_record_start(manager, &record_config) == AUDIO_MANAGER_OK) {
        ESP_LOGI(TAG, "Recording started successfully");
        
        // Let it record for the specified duration
        vTaskDelay(pdMS_TO_TICKS(record_config.duration_ms));
        
        // Stop recording
        audio_manager_record_stop(manager);
        ESP_LOGI(TAG, "Recording stopped");
    }
    
    // Cleanup
    audio_manager_deinit(manager);
}

/**
 * @brief Example: Echo/loopback functionality
 */
void audio_manager_echo_example(void)
{
    ESP_LOGI(TAG, "Starting echo example");
    
    // Initialize audio manager
    audio_manager_config_t config = AUDIO_MANAGER_DEFAULT_CONFIG();
    audio_manager_handle_t manager = audio_manager_init(&config);
    
    if (!manager) {
        ESP_LOGE(TAG, "Failed to initialize audio manager");
        return;
    }
    
    // Configure playback
    audio_playback_config_t playback_config = AUDIO_PLAYBACK_DEFAULT_CONFIG();
    playback_config.volume = 80.0;
    
    // Configure recording with echo callback
    audio_record_config_t record_config = AUDIO_RECORD_DEFAULT_CONFIG();
    record_config.gain = 35.0;
    
    // Start both playback and recording
    if (audio_manager_playback_start(manager, &playback_config) == AUDIO_MANAGER_OK &&
        audio_manager_record_start(manager, &record_config) == AUDIO_MANAGER_OK) {
        
        ESP_LOGI(TAG, "Echo mode started");
        
        // Echo loop - read from microphone and write to speaker
        uint8_t audio_buffer[1024];
        for (int i = 0; i < 100; i++) { // Run for 100 iterations
            int read_bytes = audio_manager_record_read(manager, audio_buffer, sizeof(audio_buffer), 100);
            if (read_bytes > 0) {
                audio_manager_playback_write(manager, audio_buffer, read_bytes, 100);
            }
            vTaskDelay(pdMS_TO_TICKS(10));
        }
        
        // Stop both
        audio_manager_record_stop(manager);
        audio_manager_playback_stop(manager);
        ESP_LOGI(TAG, "Echo mode stopped");
    }
    
    // Cleanup
    audio_manager_deinit(manager);
}

/**
 * @brief Example: Volume and gain control
 */
void audio_manager_control_example(void)
{
    ESP_LOGI(TAG, "Starting control example");
    
    // Initialize audio manager
    audio_manager_config_t config = AUDIO_MANAGER_DEFAULT_CONFIG();
    audio_manager_handle_t manager = audio_manager_init(&config);
    
    if (!manager) {
        ESP_LOGE(TAG, "Failed to initialize audio manager");
        return;
    }
    
    // Test volume control
    for (float volume = 0.0; volume <= 100.0; volume += 20.0) {
        ESP_LOGI(TAG, "Setting volume to %.1f", volume);
        audio_manager_playback_set_volume(manager, volume);
        vTaskDelay(pdMS_TO_TICKS(500));
    }
    
    // Test gain control
    for (float gain = 0.0; gain <= 100.0; gain += 25.0) {
        ESP_LOGI(TAG, "Setting gain to %.1f", gain);
        audio_manager_record_set_gain(manager, gain);
        vTaskDelay(pdMS_TO_TICKS(500));
    }
    
    // Cleanup
    audio_manager_deinit(manager);
}

/**
 * @brief Main example task
 */
void audio_manager_example_task(void *arg)
{
    ESP_LOGI(TAG, "Audio Manager Examples Starting");
    
    // Run examples
    audio_manager_playback_example();
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    audio_manager_record_example();
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    audio_manager_echo_example();
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    audio_manager_control_example();
    
    ESP_LOGI(TAG, "Audio Manager Examples Completed");
    vTaskDelete(NULL);
}
