#include "boot_audio.h"
#include "audio_manager.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char *TAG = "BOOT_AUDIO";

// Global boot audio state
static bool g_boot_audio_initialized = false;
static audio_manager_handle_t g_boot_audio_manager = NULL;

boot_audio_err_t boot_audio_init(void)
{
    if (g_boot_audio_initialized) {
        ESP_LOGI(TAG, "Boot audio already initialized");
        return BOOT_AUDIO_OK;
    }
    
    // Initialize MP3 system
    audio_manager_err_t ret = audio_manager_mp3_init();
    if (ret != AUDIO_MANAGER_OK) {
        ESP_LOGE(TAG, "Failed to initialize MP3 system");
        return BOOT_AUDIO_ERR_INIT_FAIL;
    }
    
    g_boot_audio_initialized = true;
    ESP_LOGI(TAG, "Boot audio system initialized");
    return BOOT_AUDIO_OK;
}

boot_audio_err_t boot_audio_play(const boot_audio_config_t *config)
{
    if (!config || !config->audio_manager) {
        ESP_LOGE(TAG, "Invalid configuration");
        return BOOT_AUDIO_ERR_INVALID_ARG;
    }
    
    if (!g_boot_audio_initialized) {
        ESP_LOGE(TAG, "Boot audio not initialized. Call boot_audio_init() first");
        return BOOT_AUDIO_ERR_NOT_INITIALIZED;
    }
    
    ESP_LOGI(TAG, "Starting boot audio playback");
    
    // Configure MP3 playback
    audio_mp3_config_t mp3_config = AUDIO_MP3_DEFAULT_CONFIG();
    mp3_config.asset_index = config->asset_index;
    mp3_config.volume = config->volume;
    mp3_config.loop = config->loop;
    mp3_config.event_cb = config->event_cb;
    mp3_config.user_ctx = config->user_ctx;
    
    // Play MP3
    audio_manager_err_t ret = audio_manager_mp3_play(config->audio_manager, &mp3_config);
    if (ret != AUDIO_MANAGER_OK) {
        ESP_LOGE(TAG, "Failed to play boot audio: %d", ret);
        return BOOT_AUDIO_ERR_PLAY_FAIL;
    }
    
    ESP_LOGI(TAG, "Boot audio playback completed");
    return BOOT_AUDIO_OK;
}

boot_audio_err_t boot_audio_play_default(audio_manager_handle_t audio_manager)
{
    if (!audio_manager) {
        ESP_LOGE(TAG, "Invalid audio manager handle");
        return BOOT_AUDIO_ERR_INVALID_ARG;
    }
    
    // Include the generated header file for asset definitions
    #include "mmap_generate_assets.h"
    
    boot_audio_config_t config = {
        .audio_manager = audio_manager,
        .asset_index = MMAP_ASSETS_BOOT_MP3,
        .volume = 70.0,
        .loop = false,
        .event_cb = NULL,
        .user_ctx = NULL
    };
    
    return boot_audio_play(&config);
}

boot_audio_err_t boot_audio_deinit(void)
{
    if (!g_boot_audio_initialized) {
        return BOOT_AUDIO_OK;
    }
    
    // Deinitialize MP3 system
    audio_manager_mp3_deinit();
    
    g_boot_audio_initialized = false;
    g_boot_audio_manager = NULL;
    
    ESP_LOGI(TAG, "Boot audio system deinitialized");
    return BOOT_AUDIO_OK;
}

// Boot audio task function
static void boot_audio_task(void *arg)
{
    boot_audio_config_t *config = (boot_audio_config_t *)arg;
    
    ESP_LOGI(TAG, "Boot audio task started");
    
    // Play boot audio
    boot_audio_err_t ret = boot_audio_play(config);
    if (ret != BOOT_AUDIO_OK) {
        ESP_LOGE(TAG, "Boot audio task failed: %d", ret);
    }
    
    // Free the config memory
    free(config);
    
    ESP_LOGI(TAG, "Boot audio task completed");
    vTaskDelete(NULL);
}

boot_audio_err_t boot_audio_play_async(const boot_audio_config_t *config)
{
    if (!config) {
        return BOOT_AUDIO_ERR_INVALID_ARG;
    }
    
    // Allocate memory for config copy
    boot_audio_config_t *config_copy = malloc(sizeof(boot_audio_config_t));
    if (!config_copy) {
        ESP_LOGE(TAG, "Failed to allocate memory for config");
        return BOOT_AUDIO_ERR_NO_MEM;
    }
    
    // Copy configuration
    memcpy(config_copy, config, sizeof(boot_audio_config_t));
    
    // Create task for async playback
    BaseType_t task_ret = xTaskCreate(
        boot_audio_task,
        "boot_audio",
        4096,
        config_copy,
        5,
        NULL
    );
    
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create boot audio task");
        free(config_copy);
        return BOOT_AUDIO_ERR_NO_MEM;
    }
    
    ESP_LOGI(TAG, "Boot audio task created for async playback");
    return BOOT_AUDIO_OK;
}

boot_audio_err_t boot_audio_play_default_async(audio_manager_handle_t audio_manager)
{
    if (!audio_manager) {
        return BOOT_AUDIO_ERR_INVALID_ARG;
    }
    
    // Include the generated header file for asset definitions
    #include "mmap_generate_assets.h"
    
    boot_audio_config_t config = {
        .audio_manager = audio_manager,
        .asset_index = MMAP_ASSETS_BOOT_MP3,
        .volume = 70.0,
        .loop = false,
        .event_cb = NULL,
        .user_ctx = NULL
    };
    
    return boot_audio_play_async(&config);
}
