#include "mp3_decoder.h"
#include "esp_log.h"
#include <stdlib.h>
#include <string.h>

static const char *TAG = "MP3_DECODER";

// MP3 frame header constants
#define MP3_FRAME_SYNC      0xFFE0
#define MP3_HEADER_SIZE     4

// Sample rates for MPEG1, MPEG2, MPEG2.5
static const int sample_rates[3][3] = {
    {44100, 48000, 32000},  // MPEG1
    {22050, 24000, 16000},  // MPEG2
    {11025, 12000, 8000}    // MPEG2.5
};

// Bitrates for MPEG1 Layer III
static const int bitrates_v1_l3[16] = {
    0, 32, 40, 48, 56, 64, 80, 96, 112, 128, 160, 192, 224, 256, 320, 0
};

// Bitrates for MPEG2/2.5 Layer III
static const int bitrates_v2_l3[16] = {
    0, 8, 16, 24, 32, 40, 48, 56, 64, 80, 96, 112, 128, 144, 160, 0
};

/**
 * @brief MP3 decoder structure
 */
typedef struct mp3_decoder_s {
    bool initialized;
    uint8_t *buffer;
    size_t buffer_size;
} mp3_decoder_t;

/**
 * @brief Find MP3 frame sync
 */
static int find_sync(const uint8_t *data, size_t size)
{
    for (size_t i = 0; i < size - 1; i++) {
        if ((data[i] == 0xFF) && ((data[i + 1] & 0xE0) == 0xE0)) {
            return i;
        }
    }
    return -1;
}

/**
 * @brief Parse MP3 frame header
 */
static mp3_decoder_err_t parse_header(const uint8_t *header, mp3_frame_info_t *info)
{
    if (!header || !info) {
        return MP3_DECODER_ERR_INVALID_ARG;
    }
    
    // Check sync word
    if (((header[0] << 8) | header[1]) & 0xFFE0 != 0xFFE0) {
        return MP3_DECODER_ERR_DECODE_FAIL;
    }
    
    // Extract fields
    int version = (header[1] >> 3) & 0x03;
    int layer = (header[1] >> 1) & 0x03;
    int bitrate_index = (header[2] >> 4) & 0x0F;
    int sample_rate_index = (header[2] >> 2) & 0x03;
    int padding = (header[2] >> 1) & 0x01;
    int channel_mode = (header[3] >> 6) & 0x03;
    
    // Validate version
    if (version == 1) { // Reserved
        return MP3_DECODER_ERR_UNSUPPORTED_FORMAT;
    }
    
    // Validate layer (we only support Layer III)
    if (layer != 1) { // Layer III
        return MP3_DECODER_ERR_UNSUPPORTED_FORMAT;
    }
    
    // Validate sample rate
    if (sample_rate_index == 3) {
        return MP3_DECODER_ERR_UNSUPPORTED_FORMAT;
    }
    
    // Validate bitrate
    if (bitrate_index == 0 || bitrate_index == 15) {
        return MP3_DECODER_ERR_UNSUPPORTED_FORMAT;
    }
    
    // Calculate values
    int version_index = (version == 3) ? 0 : ((version == 2) ? 1 : 2);
    info->sample_rate = sample_rates[version_index][sample_rate_index];
    info->channels = (channel_mode == 3) ? 1 : 2;
    info->layer = 4 - layer;
    
    // Calculate bitrate
    if (version == 3) { // MPEG1
        info->bitrate = bitrates_v1_l3[bitrate_index];
    } else { // MPEG2/2.5
        info->bitrate = bitrates_v2_l3[bitrate_index];
    }
    
    // Calculate frame size
    int samples_per_frame = (version == 3) ? 1152 : 576;
    info->frame_bytes = (samples_per_frame * info->bitrate * 1000) / (8 * info->sample_rate);
    if (padding) {
        info->frame_bytes++;
    }
    
    return MP3_DECODER_OK;
}

mp3_decoder_handle_t mp3_decoder_init(void)
{
    mp3_decoder_t *decoder = calloc(1, sizeof(mp3_decoder_t));
    if (!decoder) {
        ESP_LOGE(TAG, "Failed to allocate memory for MP3 decoder");
        return NULL;
    }
    
    // Allocate buffer for PCM output (max frame size)
    decoder->buffer_size = 1152 * 2 * 2; // max samples * channels * bytes_per_sample
    decoder->buffer = malloc(decoder->buffer_size);
    if (!decoder->buffer) {
        ESP_LOGE(TAG, "Failed to allocate buffer");
        free(decoder);
        return NULL;
    }
    
    decoder->initialized = true;
    ESP_LOGI(TAG, "MP3 decoder initialized");
    return decoder;
}

mp3_decoder_err_t mp3_decoder_deinit(mp3_decoder_handle_t handle)
{
    if (!handle) {
        return MP3_DECODER_ERR_INVALID_ARG;
    }
    
    mp3_decoder_t *decoder = (mp3_decoder_t *)handle;
    
    if (decoder->buffer) {
        free(decoder->buffer);
    }
    
    free(decoder);
    ESP_LOGI(TAG, "MP3 decoder deinitialized");
    return MP3_DECODER_OK;
}

mp3_decoder_err_t mp3_decoder_get_info(const uint8_t *mp3_data,
                                        size_t mp3_size,
                                        mp3_frame_info_t *info)
{
    if (!mp3_data || mp3_size < MP3_HEADER_SIZE || !info) {
        return MP3_DECODER_ERR_INVALID_ARG;
    }
    
    // Find first frame
    int sync_pos = find_sync(mp3_data, mp3_size);
    if (sync_pos < 0 || sync_pos + MP3_HEADER_SIZE > mp3_size) {
        return MP3_DECODER_ERR_DECODE_FAIL;
    }
    
    return parse_header(&mp3_data[sync_pos], info);
}

mp3_decoder_err_t mp3_decoder_decode(mp3_decoder_handle_t handle,
                                      const uint8_t *mp3_data,
                                      size_t mp3_size,
                                      mp3_data_callback_t callback,
                                      void *user_ctx)
{
    if (!handle || !mp3_data || mp3_size == 0 || !callback) {
        return MP3_DECODER_ERR_INVALID_ARG;
    }
    
    mp3_decoder_t *decoder = (mp3_decoder_t *)handle;
    if (!decoder->initialized) {
        return MP3_DECODER_ERR_INVALID_ARG;
    }
    
    size_t offset = 0;
    mp3_frame_info_t frame_info;
    
    ESP_LOGI(TAG, "Starting MP3 decode, size: %d bytes", mp3_size);
    
    while (offset < mp3_size) {
        // Find frame sync
        int sync_pos = find_sync(&mp3_data[offset], mp3_size - offset);
        if (sync_pos < 0) {
            ESP_LOGW(TAG, "No more frames found");
            break;
        }
        
        offset += sync_pos;
        
        if (offset + MP3_HEADER_SIZE > mp3_size) {
            ESP_LOGW(TAG, "Incomplete frame header");
            break;
        }
        
        // Parse frame header
        mp3_decoder_err_t ret = parse_header(&mp3_data[offset], &frame_info);
        if (ret != MP3_DECODER_OK) {
            ESP_LOGW(TAG, "Invalid frame header, skipping");
            offset++;
            continue;
        }
        
        // Check if we have enough data for the frame
        if (offset + frame_info.frame_bytes > mp3_size) {
            ESP_LOGW(TAG, "Incomplete frame data");
            break;
        }
        
        // For this simplified implementation, we'll generate silence
        // In a real implementation, you would decode the actual MP3 frame
        int samples_per_frame = (frame_info.sample_rate == 44100 || 
                                frame_info.sample_rate == 48000 || 
                                frame_info.sample_rate == 32000) ? 1152 : 576;
        
        // Generate silence (zeros) as placeholder
        int16_t *pcm_buffer = (int16_t *)decoder->buffer;
        memset(pcm_buffer, 0, samples_per_frame * frame_info.channels * sizeof(int16_t));
        
        // Call callback with decoded data
        int result = callback(pcm_buffer, samples_per_frame, &frame_info, user_ctx);
        if (result != 0) {
            ESP_LOGI(TAG, "Callback requested stop");
            break;
        }
        
        offset += frame_info.frame_bytes;
    }
    
    ESP_LOGI(TAG, "MP3 decode completed");
    return MP3_DECODER_OK;
}
