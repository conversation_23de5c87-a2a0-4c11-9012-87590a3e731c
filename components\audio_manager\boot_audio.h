#ifndef BOOT_AUDIO_H
#define BOOT_AUDIO_H

#include "esp_err.h"
#include "audio_manager.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Boot audio error codes
 */
typedef enum {
    BOOT_AUDIO_OK = 0,                    /*!< Success */
    BOOT_AUDIO_ERR_INVALID_ARG,           /*!< Invalid argument */
    BOOT_AUDIO_ERR_NO_MEM,                /*!< Out of memory */
    BOOT_AUDIO_ERR_INIT_FAIL,             /*!< Initialization failed */
    BOOT_AUDIO_ERR_DECODE_FAIL,           /*!< Decode failed */
    BOOT_AUDIO_ERR_PLAY_FAIL,             /*!< Playback failed */
    BOOT_AUDIO_ERR_NOT_INITIALIZED,       /*!< Boot audio not initialized */
} boot_audio_err_t;

/**
 * @brief Boot audio configuration
 */
typedef struct {
    audio_manager_handle_t audio_manager;  /*!< Audio manager handle */
    int asset_index;                       /*!< MMAP asset index for MP3 file */
    float volume;                          /*!< Playback volume (0.0-100.0) */
    bool loop;                             /*!< Loop playback */
    audio_event_callback_t event_cb;       /*!< Event callback function */
    void *user_ctx;                        /*!< User context for callback */
} boot_audio_config_t;

/**
 * @brief Default boot audio configuration
 */
#define BOOT_AUDIO_DEFAULT_CONFIG() {      \
    .audio_manager = NULL,                 \
    .volume = 60.0,                        \
    .blocking = true                       \
}

/**
 * @brief Initialize boot audio system
 * 
 * This function initializes the mmap assets system for accessing boot.mp3
 * 
 * @return BOOT_AUDIO_OK on success, error code on failure
 */
boot_audio_err_t boot_audio_init(void);

/**
 * @brief Play boot audio
 * 
 * @param config Boot audio configuration
 * @return BOOT_AUDIO_OK on success, error code on failure
 */
boot_audio_err_t boot_audio_play(const boot_audio_config_t *config);

/**
 * @brief Play boot audio with default settings
 * 
 * @param audio_manager Audio manager handle
 * @return BOOT_AUDIO_OK on success, error code on failure
 */
boot_audio_err_t boot_audio_play_default(audio_manager_handle_t audio_manager);

/**
 * @brief Deinitialize boot audio system
 * 
 * @return BOOT_AUDIO_OK on success, error code on failure
 */
boot_audio_err_t boot_audio_deinit(void);

#ifdef __cplusplus
}
#endif

#endif // BOOT_AUDIO_H
