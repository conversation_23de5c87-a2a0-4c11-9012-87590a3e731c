#ifndef AUDIO_MANAGER_H
#define AUDIO_MANAGER_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"
#include "esp_mmap_assets.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Audio manager error codes
 */
typedef enum {
    AUDIO_MANAGER_OK = 0,                    /*!< Success */
    AUDIO_MANAGER_ERR_INVALID_ARG,           /*!< Invalid argument */
    AUDIO_MANAGER_ERR_NO_MEM,                /*!< Out of memory */
    AUDIO_MANAGER_ERR_INVALID_STATE,         /*!< Invalid state */
    AUDIO_MANAGER_ERR_TIMEOUT,               /*!< Operation timeout */
    AUDIO_MANAGER_ERR_NOT_INITIALIZED,       /*!< Audio manager not initialized */
    AUDIO_MANAGER_ERR_DEVICE_NOT_FOUND,      /*!< Audio device not found */
    AUDIO_MANAGER_ERR_DEVICE_BUSY,           /*!< Audio device busy */
    AUDIO_MANAGER_ERR_UNSUPPORTED_FORMAT,    /*!< Unsupported audio format */
    AUDIO_MANAGER_ERR_HARDWARE_FAIL,         /*!< Hardware operation failed */
} audio_manager_err_t;

/**
 * @brief Audio format configuration
 */
typedef struct {
    uint32_t sample_rate;        /*!< Sample rate in Hz (8000-48000) */
    uint8_t  bits_per_sample;    /*!< Bits per sample (16, 24, 32) */
    uint8_t  channels;           /*!< Number of channels (1=mono, 2=stereo) */
} audio_format_t;

/**
 * @brief Audio playback state
 */
typedef enum {
    AUDIO_PLAYBACK_STATE_IDLE = 0,     /*!< Playback idle */
    AUDIO_PLAYBACK_STATE_PLAYING,      /*!< Currently playing */
    AUDIO_PLAYBACK_STATE_PAUSED,       /*!< Playback paused */
    AUDIO_PLAYBACK_STATE_STOPPED,      /*!< Playback stopped */
    AUDIO_PLAYBACK_STATE_ERROR,        /*!< Playback error */
} audio_playback_state_t;

/**
 * @brief Audio record state
 */
typedef enum {
    AUDIO_RECORD_STATE_IDLE = 0,       /*!< Record idle */
    AUDIO_RECORD_STATE_RECORDING,      /*!< Currently recording */
    AUDIO_RECORD_STATE_PAUSED,         /*!< Record paused */
    AUDIO_RECORD_STATE_STOPPED,        /*!< Record stopped */
    AUDIO_RECORD_STATE_ERROR,          /*!< Record error */
} audio_record_state_t;

/**
 * @brief Audio manager configuration
 */
typedef struct {
    const char *board_type;              /*!< Board type string (e.g., "C6_AMOLED_1_43") */
    audio_format_t default_playback_fmt; /*!< Default playback format */
    audio_format_t default_record_fmt;   /*!< Default record format */
    uint32_t playback_buffer_size;       /*!< Playback buffer size in bytes */
    uint32_t record_buffer_size;         /*!< Record buffer size in bytes */
    uint8_t  task_priority;              /*!< Audio task priority */
    uint32_t task_stack_size;            /*!< Audio task stack size */
} audio_manager_config_t;

/**
 * @brief Audio data callback function type
 * @param data Pointer to audio data
 * @param len Length of audio data in bytes
 * @param user_ctx User context pointer
 * @return Number of bytes processed
 */
typedef size_t (*audio_data_callback_t)(const uint8_t *data, size_t len, void *user_ctx);

/**
 * @brief Audio event callback function type
 * @param event Audio event type
 * @param data Event data pointer
 * @param user_ctx User context pointer
 */
typedef void (*audio_event_callback_t)(int event, void *data, void *user_ctx);

/**
 * @brief Audio playback configuration
 */
typedef struct {
    audio_format_t format;               /*!< Audio format */
    float volume;                        /*!< Volume level (0.0-100.0) */
    bool loop;                           /*!< Loop playback */
    audio_event_callback_t event_cb;     /*!< Event callback function */
    void *user_ctx;                      /*!< User context for callback */
} audio_playback_config_t;

/**
 * @brief Audio record configuration
 */
typedef struct {
    audio_format_t format;               /*!< Audio format */
    float gain;                          /*!< Input gain (0.0-100.0) */
    uint32_t duration_ms;                /*!< Record duration in milliseconds (0=infinite) */
    audio_data_callback_t data_cb;       /*!< Data callback function */
    audio_event_callback_t event_cb;     /*!< Event callback function */
    void *user_ctx;                      /*!< User context for callback */
} audio_record_config_t;

/**
 * @brief Audio manager handle
 */
typedef struct audio_manager_s *audio_manager_handle_t;

/**
 * @brief MP3 playback configuration
 */
typedef struct {
    int asset_index;                     /*!< MMAP asset index for MP3 file */
    float volume;                        /*!< Volume level (0.0-100.0) */
    bool loop;                           /*!< Loop playback */
    audio_event_callback_t event_cb;     /*!< Event callback function */
    void *user_ctx;                      /*!< User context for callback */
} audio_mp3_config_t;

/**
 * @brief Default audio manager configuration
 */
#define AUDIO_MANAGER_DEFAULT_CONFIG() {                    \
    .board_type = "C6_AMOLED_1_43",                        \
    .default_playback_fmt = {                              \
        .sample_rate = 24000,                              \
        .bits_per_sample = 16,                             \
        .channels = 2                                      \
    },                                                     \
    .default_record_fmt = {                                \
        .sample_rate = 24000,                              \
        .bits_per_sample = 32,                             \
        .channels = 2                                      \
    },                                                     \
    .playback_buffer_size = 4096,                          \
    .record_buffer_size = 4096,                            \
    .task_priority = 5,                                    \
    .task_stack_size = 4096                                \
}

/**
 * @brief Default playback configuration
 */
#define AUDIO_PLAYBACK_DEFAULT_CONFIG() {                  \
    .format = {                                            \
        .sample_rate = 24000,                              \
        .bits_per_sample = 16,                             \
        .channels = 2                                      \
    },                                                     \
    .volume = 60.0,                                        \
    .loop = false,                                         \
    .event_cb = NULL,                                      \
    .user_ctx = NULL                                       \
}

/**
 * @brief Default record configuration
 */
#define AUDIO_RECORD_DEFAULT_CONFIG() {                    \
    .format = {                                            \
        .sample_rate = 24000,                              \
        .bits_per_sample = 32,                             \
        .channels = 2                                      \
    },                                                     \
    .gain = 35.0,                                          \
    .duration_ms = 0,                                      \
    .data_cb = NULL,                                       \
    .event_cb = NULL,                                      \
    .user_ctx = NULL                                       \
}

/**
 * @brief Default MP3 playback configuration
 */
#define AUDIO_MP3_DEFAULT_CONFIG() {                       \
    .asset_index = 0,                                      \
    .volume = 60.0,                                        \
    .loop = false,                                         \
    .event_cb = NULL,                                      \
    .user_ctx = NULL                                       \
}

/**
 * @brief Initialize audio manager
 * 
 * @param config Audio manager configuration
 * @return Audio manager handle on success, NULL on failure
 */
audio_manager_handle_t audio_manager_init(const audio_manager_config_t *config);

/**
 * @brief Deinitialize audio manager
 * 
 * @param handle Audio manager handle
 * @return AUDIO_MANAGER_OK on success, error code on failure
 */
audio_manager_err_t audio_manager_deinit(audio_manager_handle_t handle);

/**
 * @brief Start audio playback
 * 
 * @param handle Audio manager handle
 * @param config Playback configuration
 * @return AUDIO_MANAGER_OK on success, error code on failure
 */
audio_manager_err_t audio_manager_playback_start(audio_manager_handle_t handle, 
                                                  const audio_playback_config_t *config);

/**
 * @brief Stop audio playback
 * 
 * @param handle Audio manager handle
 * @return AUDIO_MANAGER_OK on success, error code on failure
 */
audio_manager_err_t audio_manager_playback_stop(audio_manager_handle_t handle);

/**
 * @brief Pause audio playback
 * 
 * @param handle Audio manager handle
 * @return AUDIO_MANAGER_OK on success, error code on failure
 */
audio_manager_err_t audio_manager_playback_pause(audio_manager_handle_t handle);

/**
 * @brief Resume audio playback
 * 
 * @param handle Audio manager handle
 * @return AUDIO_MANAGER_OK on success, error code on failure
 */
audio_manager_err_t audio_manager_playback_resume(audio_manager_handle_t handle);

/**
 * @brief Write audio data for playback
 * 
 * @param handle Audio manager handle
 * @param data Audio data buffer
 * @param len Data length in bytes
 * @param timeout_ms Timeout in milliseconds
 * @return Number of bytes written, or negative error code
 */
int audio_manager_playback_write(audio_manager_handle_t handle, 
                                 const uint8_t *data, 
                                 size_t len, 
                                 uint32_t timeout_ms);

/**
 * @brief Set playback volume
 * 
 * @param handle Audio manager handle
 * @param volume Volume level (0.0-100.0)
 * @return AUDIO_MANAGER_OK on success, error code on failure
 */
audio_manager_err_t audio_manager_playback_set_volume(audio_manager_handle_t handle, float volume);

/**
 * @brief Get playback state
 * 
 * @param handle Audio manager handle
 * @return Current playback state
 */
audio_playback_state_t audio_manager_playback_get_state(audio_manager_handle_t handle);

/**
 * @brief Start audio recording
 * 
 * @param handle Audio manager handle
 * @param config Record configuration
 * @return AUDIO_MANAGER_OK on success, error code on failure
 */
audio_manager_err_t audio_manager_record_start(audio_manager_handle_t handle, 
                                                const audio_record_config_t *config);

/**
 * @brief Stop audio recording
 * 
 * @param handle Audio manager handle
 * @return AUDIO_MANAGER_OK on success, error code on failure
 */
audio_manager_err_t audio_manager_record_stop(audio_manager_handle_t handle);

/**
 * @brief Pause audio recording
 * 
 * @param handle Audio manager handle
 * @return AUDIO_MANAGER_OK on success, error code on failure
 */
audio_manager_err_t audio_manager_record_pause(audio_manager_handle_t handle);

/**
 * @brief Resume audio recording
 * 
 * @param handle Audio manager handle
 * @return AUDIO_MANAGER_OK on success, error code on failure
 */
audio_manager_err_t audio_manager_record_resume(audio_manager_handle_t handle);

/**
 * @brief Read recorded audio data
 * 
 * @param handle Audio manager handle
 * @param data Buffer to store audio data
 * @param len Buffer length in bytes
 * @param timeout_ms Timeout in milliseconds
 * @return Number of bytes read, or negative error code
 */
int audio_manager_record_read(audio_manager_handle_t handle, 
                              uint8_t *data, 
                              size_t len, 
                              uint32_t timeout_ms);

/**
 * @brief Set record gain
 * 
 * @param handle Audio manager handle
 * @param gain Gain level (0.0-100.0)
 * @return AUDIO_MANAGER_OK on success, error code on failure
 */
audio_manager_err_t audio_manager_record_set_gain(audio_manager_handle_t handle, float gain);

/**
 * @brief Get record state
 *
 * @param handle Audio manager handle
 * @return Current record state
 */
audio_record_state_t audio_manager_record_get_state(audio_manager_handle_t handle);

/**
 * @brief Initialize MP3 playback system
 *
 * @return AUDIO_MANAGER_OK on success, error code on failure
 */
audio_manager_err_t audio_manager_mp3_init(void);

/**
 * @brief Deinitialize MP3 playback system
 *
 * @return AUDIO_MANAGER_OK on success, error code on failure
 */
audio_manager_err_t audio_manager_mp3_deinit(void);

/**
 * @brief Play MP3 file from MMAP assets
 *
 * @param handle Audio manager handle
 * @param config MP3 playback configuration
 * @return AUDIO_MANAGER_OK on success, error code on failure
 */
audio_manager_err_t audio_manager_mp3_play(audio_manager_handle_t handle,
                                            const audio_mp3_config_t *config);

/**
 * @brief Play MP3 file from MMAP assets with default settings
 *
 * @param handle Audio manager handle
 * @param asset_index MMAP asset index
 * @return AUDIO_MANAGER_OK on success, error code on failure
 */
audio_manager_err_t audio_manager_mp3_play_simple(audio_manager_handle_t handle,
                                                   int asset_index);

/**
 * @brief Stop MP3 playback
 *
 * @param handle Audio manager handle
 * @return AUDIO_MANAGER_OK on success, error code on failure
 */
audio_manager_err_t audio_manager_mp3_stop(audio_manager_handle_t handle);

#ifdef __cplusplus
}
#endif

#endif // AUDIO_MANAGER_H
